
#open_high_return_5d

python trade.py --weight_type best_ric --output_dir kzz/V1 --top_n 10 --run_id V1 --start_date 2025-06-01 --end_date 2025-07-18 --data_path tushare_data/cb_factors.csv


python trade.py --weight_type best_ric --output_dir kzz/V2 --top_n 10 --run_id V2 --start_date 2025-06-01 --end_date 2025-07-18 --data_path tushare_data/cb_factors.csv


python trade.py --weight_type best --output_dir kzz/1d_best --top_n 10 --run_id 20250705_114830 --start_date 2025-06-01 --end_date 2025-07-04 --data_path tushare_data/cb_factors.csv










python trade.py --output_dir trade0523_24 --weight_type best_ric --top_n 20 --run_id 20250522_131505_2024_01_o --start_date 2024-01-01 --end_date 2025-05-20 --data_path tushare_data/cb_factors.csv


python trade.py --output_dir trade0523_24_less_1_5 --weight_type best_ric --top_n 20 --run_id 20250522_153348_2024_01_less1_5 --start_date 2025-01-01 --end_date 2025-05-23 --data_path tushare_data/cb_factors.csv

python trade.py --output_dir trade0523_24 --weight_type best_ric --top_n 20 --run_id 20250522_131505_2024_01_o --start_date 2025-01-01 --end_date 2025-05-23 --data_path tushare_data/cb_factors.csv



python trade.py --output_dir trade0523 --weight_type best_ric --top_n 20 --run_id 20250524_030658_5day --start_date 2025-01-01 --end_date 2025-05-23 --data_path tushare_data/cb_factors.csv


python trade.py --output_dir trade0523_5day --weight_type best_ric --top_n 20 --run_id 20250524_090048_5days --start_date 2025-01-01 --end_date 2025-05-23 --data_path tushare_data/cb_factors.csv

python trade.py --output_dir trade0523_1day --weight_type best_ric --top_n 20 --run_id 20250524_090326_1day --start_date 2025-01-01 --end_date 2025-05-23 --data_path tushare_data/cb_factors.csv


跳空高开的阴线+长上下影 ×
cci多头买入            ✓


python trade_new.py --output_dir trade0523_5d --weight_type best --top_n 20 --run_id 20250526_032859_5d_best --start_date 2025-05-01 --end_date 2025-05-26 --data_path tushare_data/cb_factors.csv


python trade_new.py --output_dir trade0530_5d --top_n 20 --run_id 20250528_122715 --start_date 2025-05-29 --end_date 2025-05-30 --data_path tushare_data/cb_factors.csv

python trade_new.py --output_dir tradenew0528_5d --top_n 20 --weight_type best_ric  --run_id 20250529_134749 --start_date 2025-05-23 --end_date 2025-05-29 --data_path tushare_data/cb_factors.csv


帮我生成50个十分敏感的可转债的因子,奖励上涨初期,惩罚上涨末期容易下跌的因子,捕捉温和上涨但是不是上涨末期容易下跌的因子。我不要这种简单因子,我要非常非常创新的因子,非常非常敏感的因子,来适合我的目标函数:(第二天到第六天中最高价的最大值/第二天的开盘-1)*100。 我要非常非常敏感的因子。我有可转债的数据s_codestrY转债代码trade_datestrY交易日期pre_closefloatY昨收盘价(元)openfloatY开盘价(元)highfloatY最高价(元)lowfloatY最低价(元)closefloatY收盘价(元)changefloatY涨跌(元)pct_chgfloatY涨跌幅(%)volfloatY成交量(手)amountfloatY成交金额(万元)bond_valuefloatN纯债价值bond_over_ratefloatN纯债溢价率(%)cb_valuefloatN转股价值cb_over_ratefloatN转股溢价率(%)。对应正股的数据:ts_codestr股票代码trade_datestr交易日期openfloat开盘价highfloat最高价lowfloat最低价closefloat收盘价pre_closefloat昨收价【除权价,前复权】changefloat涨跌额pct_chgfloat涨跌幅 【基于除权后的昨收计算的涨跌幅:(今收-除权昨收)/除权昨收 】volfloat成交量 (手)amountfloat成交额 (千元)。 我需要的是融合了前沿科学理论、包含复杂的非线性变换、捕捉高阶相互作用、时间序列的深层模式或者更多更加先进的模式,非常创新,从未有过的模式。 调用函数的参数值中不要出现min跟max,你写出函数跟如何在calculate_factors_for_bond调用就行了。 注释使用简体中文,在如下的代码中写,写成def函数,然后再调用,因子不要引入时间窗口,我就要当天的。我现在已经有混沌洛伦兹吸引子相变因子非线性动力学相位耦合因子拓扑相变序参量非平衡态统计力学因子引力波扰动检测因子虫洞穿越概率因子暗能量加速膨胀因子蛋白质折叠自由能因子普朗克尺度量子引力因子量子色动力学夸克禁闭因子哥德尔不完全性定理证明复杂度因子量子纠缠熵密度因子拓扑绝缘体边缘态因子自组织临界雪崩因子量子霍尔效应电导平台因子玻色-爱因斯坦凝聚相干因子引力透镜放大因子中子星脉冲周期漂移因子额外维度卡鲁扎-克莱因激发因子量子退相干速率因子非阿贝尔规范场涡旋因子量子临界标度因子全息对偶AdS/CFT因子量子多体局域化因子量子自旋液体因子弦网凝聚拓扑序因子量子疤痕态因子非厄米拓扑因子量子热机效率因子时间晶体周期性因子量子达尔文主义因子量子Zeno效应因子量子walk扩散因子拓扑量子计算anyonic编织因子量子纠错稳定子因子量子电池充电功率因子光子晶体带隙因子非平衡态势能因子神经激活因子拓扑相变因子相空间重构因子非线性共振因子量子隧穿势垒穿透因子自旋玻璃能量景观因子声子-极化子耦合因子准晶体五重对称因子孤子稳定传播因子磁性斯格明子拓扑保护因子量子蒙特卡洛路径积分因子维度约化重整化群流因子



python trade_new.py --output_dir trade0523_1d_c --weight_type best_ric --top_n 20 --run_id 20250526_054257_1d_close_best_ric --start_date 2025-01-01 --end_date 2025-05-14 --data_path tushare_data/cb_factors.csv










!python train.py --data_path tushare_data/cb_factors_train.csv --epochs 3 --batch_size 64 --end_date 2024-08-01 --end_date 2025-01-01 







20250526_033126_1d_ric
周五的 19
周四的 10


20250526_032859_5d_best
周二的1 周五的5
周四的1 周五的1





python eval.py --model_path output\20250511_051632\best_ric_model.pt --data_path tushare_data\cb_factors_train.csv --start_date 2025-01-01 --end_date 2025-04-15