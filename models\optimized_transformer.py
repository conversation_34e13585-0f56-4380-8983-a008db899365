import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
import sys
import os

# 导入配置
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import config


class RMSNorm(nn.Module):
    """RMS归一化层"""
    def __init__(self, dim, eps=1e-5):
        super().__init__()
        self.eps = eps
        self.weight = nn.Parameter(torch.ones(dim))

    def forward(self, x):
        rms = torch.sqrt(torch.mean(x**2, dim=-1, keepdim=True) + self.eps)
        return self.weight * x / rms


class SwiGLU(nn.Module):
    """SwiGLU (Swish-Gated Linear Unit)"""
    def __init__(self, in_features, hidden_features=None, out_features=None, bias=False):
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features
        
        self.w1 = nn.Linear(in_features, hidden_features, bias=bias)
        self.w2 = nn.Linear(in_features, hidden_features, bias=bias)
        self.w3 = nn.Linear(hidden_features, out_features, bias=bias)
        self.act = nn.SiLU()

    def forward(self, x):
        return self.w3(self.act(self.w1(x)) * self.w2(x))


class RotaryPositionalEmbedding(nn.Module):
    """旋转位置编码 (RoPE)"""
    def __init__(self, dim, max_seq_len=1024, base=10000):
        super().__init__()
        self.dim = dim
        self.max_seq_len = max_seq_len
        self.base = base
        
        inv_freq = 1.0 / (base ** (torch.arange(0, dim, 2).float() / dim))
        self.register_buffer("inv_freq", inv_freq)
        
        # 预计算位置编码
        seq_len = max_seq_len
        t = torch.arange(seq_len, dtype=torch.float32)
        freqs = torch.outer(t, inv_freq)
        emb = torch.cat((freqs, freqs), dim=-1)
        
        self.register_buffer("cos_cached", emb.cos()[None, None, :, :])
        self.register_buffer("sin_cached", emb.sin()[None, None, :, :])

    def forward(self, q, k):
        seq_len = q.shape[-2]
        cos = self.cos_cached[:, :, :seq_len, ...]
        sin = self.sin_cached[:, :, :seq_len, ...]
        
        def rotate_half(x):
            x1, x2 = x[..., : self.dim // 2], x[..., self.dim // 2 :]
            return torch.cat((-x2, x1), dim=-1)

        q_rotated = (q * cos) + (rotate_half(q) * sin)
        k_rotated = (k * cos) + (rotate_half(k) * sin)
        
        return q_rotated, k_rotated


class MultiHeadAttention(nn.Module):
    """多头自注意力机制"""
    def __init__(self, d_model, num_heads, dropout=0.1):
        super().__init__()
        assert d_model % num_heads == 0
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.head_dim = d_model // num_heads
        
        self.q_proj = nn.Linear(d_model, d_model)
        self.k_proj = nn.Linear(d_model, d_model)
        self.v_proj = nn.Linear(d_model, d_model)
        self.out_proj = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.scale = self.head_dim ** -0.5
        
    def forward(self, x, mask=None):
        batch_size, seq_len, d_model = x.shape
        
        q = self.q_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        k = self.k_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        v = self.v_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        
        scores = torch.matmul(q, k.transpose(-2, -1)) * self.scale
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        out = torch.matmul(attn_weights, v)
        out = out.transpose(1, 2).contiguous().view(batch_size, seq_len, d_model)
        out = self.out_proj(out)
        
        return out


class IntraStockAttention(nn.Module):
    """
    股内(Intra-Stock)注意力机制
    对每只股票自己的时间序列进行attention，捕捉其自身的时序动态
    """
    def __init__(self, d_model, num_heads, dropout=0.1):
        super().__init__()
        self.attention = MultiHeadAttention(d_model, num_heads, dropout)
        self.norm = RMSNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, rope):
        """
        x: [batch_size, seq_len, d_model]
        返回: [batch_size, seq_len, d_model] - 保留时间维度的局部嵌入序列
        """
        # 应用注意力机制
        attended = self.attention(x)
        # 残差连接和归一化
        output = self.norm(x + self.dropout(attended))
        return output


class InterStockAttention(nn.Module):
    """
    股间(Inter-Stock)注意力机制
    在每一个时间点t，对所有股票的局部嵌入进行attention，捕捉该时刻的股票间关联
    """
    def __init__(self, d_model, num_heads, num_stocks, dropout=0.1):
        super().__init__()
        self.d_model = d_model
        self.num_heads = num_heads
        self.num_stocks = num_stocks
        self.head_dim = d_model // num_heads
        
        # 为股间交互设计的注意力机制
        self.q_proj = nn.Linear(d_model, d_model)
        self.k_proj = nn.Linear(d_model, d_model)
        self.v_proj = nn.Linear(d_model, d_model)
        self.out_proj = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.scale = self.head_dim ** -0.5
        self.norm = RMSNorm(d_model)
        
    def forward(self, x):
        """
        x: [batch_size, seq_len, d_model] - 来自股内attention的输出
        在每个时间步上进行股间交互
        """
        batch_size, seq_len, d_model = x.shape
        
        # 对每个时间步分别进行股间attention
        outputs = []
        for t in range(seq_len):
            x_t = x[:, t, :]  # [batch_size, d_model]
            
            # 计算Q, K, V
            q = self.q_proj(x_t).view(batch_size, self.num_heads, self.head_dim)
            k = self.k_proj(x_t).view(batch_size, self.num_heads, self.head_dim)
            v = self.v_proj(x_t).view(batch_size, self.num_heads, self.head_dim)
            
            # 计算注意力分数 (batch内的股票间交互)
            scores = torch.matmul(q, k.transpose(-2, -1)) * self.scale  # [batch_size, num_heads, num_heads]
            attn_weights = F.softmax(scores, dim=-1)
            attn_weights = self.dropout(attn_weights)
            
            # 应用注意力
            attended = torch.matmul(attn_weights, v)  # [batch_size, num_heads, head_dim]
            attended = attended.view(batch_size, d_model)
            attended = self.out_proj(attended)
            
            # 残差连接和归一化
            output_t = self.norm(x_t + self.dropout(attended))
            outputs.append(output_t.unsqueeze(1))
        
        return torch.cat(outputs, dim=1)  # [batch_size, seq_len, d_model]


class MASTERTransformerBlock(nn.Module):
    """
    MASTER风格的双层Transformer块
    先进行股内(Intra-Stock)聚合，再进行股间(Inter-Stock)聚合
    """
    def __init__(self, d_model, num_heads, num_stocks, dropout=0.1):
        super().__init__()
        
        # 股内注意力
        self.intra_stock_attention = IntraStockAttention(d_model, num_heads, dropout)
        
        # 股间注意力
        self.inter_stock_attention = InterStockAttention(d_model, num_heads, num_stocks, dropout)
        
        # FFN层
        self.norm = RMSNorm(d_model)
        self.ffn = SwiGLU(in_features=d_model, hidden_features=d_model * 4, out_features=d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, rope):
        """
        x: [batch_size, seq_len, d_model]
        """
        # 1. 股内聚合：对每只股票自己的时间序列进行attention
        intra_output = self.intra_stock_attention(x, rope)
        
        # 2. 股间聚合：在每个时间点对所有股票进行attention
        inter_output = self.inter_stock_attention(intra_output)
        
        # 3. FFN层
        ffn_input = self.norm(inter_output)
        ffn_output = self.ffn(ffn_input)
        final_output = inter_output + self.dropout(ffn_output)
        
        return final_output


class HistoricalAttentionAggregator(nn.Module):
    """
    历史信息聚合机制
    使用最后一个时间步的嵌入作为查询(Query)，去attend历史上所有的嵌入
    """
    def __init__(self, d_model, num_heads, dropout=0.1):
        super().__init__()
        self.d_model = d_model
        self.num_heads = num_heads
        self.head_dim = d_model // num_heads
        
        self.q_proj = nn.Linear(d_model, d_model)
        self.k_proj = nn.Linear(d_model, d_model)
        self.v_proj = nn.Linear(d_model, d_model)
        self.out_proj = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.scale = self.head_dim ** -0.5
        
    def forward(self, x):
        """
        x: [batch_size, seq_len, d_model] - 来自MASTER Transformer的输出
        返回: [batch_size, d_model] - 聚合后的最终表示
        """
        batch_size, seq_len, d_model = x.shape
        
        # 使用最后一个时间步作为查询
        query_input = x[:, -1, :]  # [batch_size, d_model]
        
        # 所有时间步作为键和值
        key_value_input = x  # [batch_size, seq_len, d_model]
        
        # 计算Q, K, V
        q = self.q_proj(query_input).view(batch_size, 1, self.num_heads, self.head_dim).transpose(1, 2)
        k = self.k_proj(key_value_input).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        v = self.v_proj(key_value_input).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        
        # 计算注意力分数
        scores = torch.matmul(q, k.transpose(-2, -1)) * self.scale  # [batch_size, num_heads, 1, seq_len]
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # 应用注意力
        attended = torch.matmul(attn_weights, v)  # [batch_size, num_heads, 1, head_dim]
        attended = attended.transpose(1, 2).contiguous().view(batch_size, d_model)
        
        # 输出投影
        output = self.out_proj(attended)
        
        return output


class OptimizedTransformerModel(nn.Module):
    """
    优化后的Transformer模型
    只保留Transformer相关组件，实现MASTER风格的双层架构和历史信息聚合
    """
    def __init__(
        self,
        input_dim,
        d_model=config.D_MODEL,
        n_layer=config.N_LAYER,
        num_heads=config.NUM_HEADS,
        dropout=config.DROPOUT,
        lookback_window=None
    ):
        super().__init__()

        # 输入投影层
        self.input_projection = nn.Linear(input_dim, d_model)

        # RoPE位置编码
        assert d_model % num_heads == 0, "d_model must be divisible by num_heads"
        effective_lookback = lookback_window if lookback_window is not None else config.LOOKBACK_WINDOW
        self.rope = RotaryPositionalEmbedding(dim=d_model // num_heads, max_seq_len=effective_lookback * 2)

        # MASTER风格的双层Transformer块
        # 注意：这里的num_stocks参数需要根据实际情况调整，暂时设为input_dim
        self.transformer_blocks = nn.ModuleList([
            MASTERTransformerBlock(
                d_model=d_model,
                num_heads=num_heads,
                num_stocks=input_dim,  # 假设每个特征对应一只股票
                dropout=dropout
            ) for _ in range(n_layer)
        ])

        # 历史信息聚合器
        self.historical_aggregator = HistoricalAttentionAggregator(d_model, num_heads, dropout)

        # 输出头
        self.output_head = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, 1)
        )

    def forward(self, x):
        """
        x: [batch_size, seq_len, input_dim]
        """
        # 输入投影
        x = self.input_projection(x)  # [batch_size, seq_len, d_model]

        # 通过MASTER风格的Transformer块
        for block in self.transformer_blocks:
            x = block(x, self.rope)

        # 历史信息聚合
        aggregated = self.historical_aggregator(x)  # [batch_size, d_model]

        # 输出预测
        output = self.output_head(aggregated)  # [batch_size, 1]

        return output.squeeze(-1)  # [batch_size]


# 为了向后兼容，创建一个别名
SAMBAModel = OptimizedTransformerModel
