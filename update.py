import os
import argparse
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from datetime import datetime
import json
import pickle
from tqdm import tqdm

# 导入自定义模块
import config
from data_loader import DataProcessor
from models.samba_model import SAMBAModel
from utils import setup_seed, plot_training_curves, validate

def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description='SAMBA模型更新训练')
    parser.add_argument('--model_path', type=str, required=True, help='要更新的模型路径，例如：output/20240101_123456/best_ric_model.pt')
    parser.add_argument('--data_path', type=str, default=config.DATA_PATH, help='CSV数据文件路径')
    parser.add_argument('--epochs', type=int, default=30, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=config.BATCH_SIZE, help='批大小')
    parser.add_argument('--lr', type=float, default=0.0001, help='学习率（建议比初始训练更低）')
    parser.add_argument('--weight_decay', type=float, default=config.WEIGHT_DECAY, help='权重衰减')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--start_date', type=str, required=True, help='微调数据开始日期(YYYY-MM-DD)')
    parser.add_argument('--end_date', type=str, default=None, help='微调数据截止日期(YYYY-MM-DD)，不指定则使用数据中的最新日期')
    return parser.parse_args()

def update_model(args):
    """
    模型微调更新主函数
    
    参数:
        args: 命令行参数
    """
    # 设置随机种子
    setup_seed(args.seed)
    
    # 生成更新ID
    update_id = datetime.now().strftime("%Y%m%d_%H%M%S")
    print(f"更新ID: {update_id}")
    
    # 解析原始模型路径
    model_dir = os.path.dirname(args.model_path)
    model_name = os.path.basename(args.model_path)
    
    # 检查原始模型是否存在
    if not os.path.exists(args.model_path):
        raise FileNotFoundError(f"模型文件不存在: {args.model_path}")
    
    # 加载原始模型的配置和数据信息
    config_path = os.path.join(model_dir, 'config.json')
    data_info_path = os.path.join(model_dir, 'data_info.pkl')
    
    # 检查配置和数据信息是否存在
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    if not os.path.exists(data_info_path):
        raise FileNotFoundError(f"数据信息文件不存在: {data_info_path}")
    
    # 加载配置和数据信息
    with open(config_path, 'r') as f:
        original_config = json.load(f)
    
    with open(data_info_path, 'rb') as f:
        data_info = pickle.load(f)
    
    # 创建更新输出目录
    update_dir = os.path.join(config.OUTPUT_DIR, f"update_{update_id}")
    os.makedirs(update_dir, exist_ok=True)
    
    # 保存更新配置
    update_config = {
        'original_model_path': args.model_path,
        'data_path': args.data_path,
        'epochs': args.epochs,
        'batch_size': args.batch_size,
        'learning_rate': args.lr,
        'weight_decay': args.weight_decay,
        'start_date': args.start_date,
        'end_date': args.end_date,
        'seed': args.seed,
        'update_id': update_id,
        'original_config': original_config
    }
    
    with open(os.path.join(update_dir, 'update_config.json'), 'w') as f:
        json.dump(update_config, f, indent=4)
    
    print("更新配置信息:")
    for k, v in update_config.items():
        if k != 'original_config':
            print(f"  {k}: {v}")
    
    # 使用原始模型的标准化器加载数据
    print("准备微调数据...")
    scaler = data_info['scaler']
    
    data_processor = DataProcessor(
        data_path=args.data_path,
        factors=original_config['factors'],
        lookback=config.LOOKBACK_WINDOW,
        batch_size=args.batch_size,
        start_date=args.start_date,
        end_date=args.end_date
    )
    
    # 设置预先训练好的标准化器
    data_processor.scaler = scaler
    
    # 加载数据
    dataloaders, update_data_info = data_processor.prepare_data()
    
    # 保存更新使用的数据信息
    with open(os.path.join(update_dir, 'update_data_info.pkl'), 'wb') as f:
        pickle.dump(update_data_info, f)
    
    # 初始化模型
    print("初始化优化后的Transformer模型...")
    model = SAMBAModel(
        input_dim=len(data_info['feature_names']),
        d_model=original_config['d_model'],
        n_layer=original_config['n_layer'],
        num_heads=original_config['num_heads'],
        dropout=original_config.get('dropout', config.DROPOUT),
        lookback_window=original_config.get('lookback_window', config.LOOKBACK_WINDOW)
    ).to(config.DEVICE)
    
    # 加载原模型权重
    print(f"加载原始模型权重: {args.model_path}")
    checkpoint = torch.load(args.model_path, map_location=config.DEVICE)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # 定义损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.Adam(
        model.parameters(), 
        lr=args.lr,
        weight_decay=args.weight_decay
    )
    
    # 初始化微调变量
    best_val_loss = float('inf')
    best_ric = float('-inf')
    train_losses = []
    val_losses = []
    val_metrics_history = []
    early_stop_counter = 0
    
    # 微调循环
    print(f"开始微调，共 {args.epochs} 轮...")
    for epoch in range(args.epochs):
        print(f"\n第 {epoch+1}/{args.epochs} 轮:")
        
        # 训练一个epoch
        model.train()
        total_loss = 0
        
        for features, targets in tqdm(dataloaders['train'], desc="微调中"):
            features, targets = features.to(config.DEVICE), targets.to(config.DEVICE)
            
            # 前向传播
            outputs = model(features)
            loss = criterion(outputs, targets)
            
            # 反向传播和优化
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item() * features.size(0)
        
        train_loss = total_loss / len(dataloaders['train'].dataset)
        train_losses.append(train_loss)
        
        # 在验证集上评估
        val_loss, val_metrics = validate(
            model, dataloaders['val'], criterion, config.DEVICE
        )
        val_losses.append(val_loss)
        val_metrics_history.append(val_metrics)
        
        # 打印当前结果
        print(f"微调损失: {train_loss:.6f}, 验证损失: {val_loss:.6f}")
        print(f"验证指标: RMSE={val_metrics['rmse']:.6f}, R²={val_metrics['r2']:.6f}, IC={val_metrics['ic']:.6f}, RIC={val_metrics['ric']:.6f}")
        
        # 检查是否为最佳损失模型
        is_best = val_loss < best_val_loss
        if is_best:
            best_val_loss = val_loss
            early_stop_counter = 0
            print("发现新的最佳损失模型!")
            
            # 保存最佳损失模型
            best_model_path = os.path.join(update_dir, 'best_model.pt')
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_metrics': val_metrics,
                'best_val_loss': best_val_loss,
                'original_model_path': args.model_path
            }, best_model_path)
            print(f"已保存最佳损失模型至 {best_model_path}")
        else:
            early_stop_counter += 1
            print(f"早停计数: {early_stop_counter}/{config.EARLY_STOP_PATIENCE}")
        
        # 检查是否为最佳RIC模型
        is_best_ric = val_metrics['ric'] > best_ric
        if is_best_ric:
            best_ric = val_metrics['ric']
            print("发现新的最佳RIC模型!")
            
            # 保存最佳RIC模型
            best_ric_model_path = os.path.join(update_dir, 'best_ric_model.pt')
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_metrics': val_metrics,
                'best_ric': best_ric,
                'original_model_path': args.model_path
            }, best_ric_model_path)
            print(f"已保存最佳RIC模型至 {best_ric_model_path}")
        
        # 判断是否早停
        if early_stop_counter >= config.EARLY_STOP_PATIENCE:
            print(f"验证损失 {config.EARLY_STOP_PATIENCE} 轮未改善，提前停止微调")
            break
        
        # 每5轮绘制并保存训练曲线
        if (epoch + 1) % 5 == 0 or epoch == args.epochs - 1:
            plot_training_curves(
                train_losses, val_losses, val_metrics_history, 
                save_path=os.path.join(update_dir, f'update_curves_epoch_{epoch+1}.png')
            )
    
    # 在测试集上评估最佳损失模型
    best_model_path = os.path.join(update_dir, 'best_model.pt')
    if os.path.exists(best_model_path):
        checkpoint = torch.load(best_model_path, map_location=config.DEVICE)
        model.load_state_dict(checkpoint['model_state_dict'])
        
        print("\n在测试集上评估最佳损失模型...")
        test_loss, test_metrics = validate(
            model, dataloaders['test'], criterion, config.DEVICE
        )
        print(f"测试损失: {test_loss:.6f}")
        print(f"测试指标: RMSE={test_metrics['rmse']:.6f}, R²={test_metrics['r2']:.6f}, IC={test_metrics['ic']:.6f}, RIC={test_metrics['ric']:.6f}")
    
    # 在测试集上评估最佳RIC模型
    best_ric_model_path = os.path.join(update_dir, 'best_ric_model.pt')
    if os.path.exists(best_ric_model_path):
        checkpoint_ric = torch.load(best_ric_model_path, map_location=config.DEVICE)
        model.load_state_dict(checkpoint_ric['model_state_dict'])
        
        print("\n在测试集上评估最佳RIC模型...")
        test_loss_ric, test_metrics_ric = validate(
            model, dataloaders['test'], criterion, config.DEVICE
        )
        print(f"测试损失: {test_loss_ric:.6f}")
        print(f"测试指标: RMSE={test_metrics_ric['rmse']:.6f}, R²={test_metrics_ric['r2']:.6f}, IC={test_metrics_ric['ic']:.6f}, RIC={test_metrics_ric['ric']:.6f}")
    
    # 保存测试结果
    results = {
        'test_loss': test_loss if 'test_loss' in locals() else None,
        'test_metrics': test_metrics if 'test_metrics' in locals() else None,
        'test_loss_ric': test_loss_ric if 'test_loss_ric' in locals() else None,
        'test_metrics_ric': test_metrics_ric if 'test_metrics_ric' in locals() else None,
        'train_history': {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'val_metrics': val_metrics_history
        },
        'epochs_completed': len(train_losses),
        'best_val_loss': best_val_loss,
        'best_ric': best_ric,
        'original_model_path': args.model_path
    }
    
    class NumpyEncoder(json.JSONEncoder):
        """处理NumPy类型的JSON编码器"""
        def default(self, obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            return super(NumpyEncoder, self).default(obj)
    
    with open(os.path.join(update_dir, 'update_results.json'), 'w') as f:
        json.dump(results, f, indent=4, cls=NumpyEncoder)
    
    print(f"\n微调完成！更新后的模型已保存至: {update_dir}")
    return update_dir

if __name__ == "__main__":
    args = parse_args()
    update_model(args)
